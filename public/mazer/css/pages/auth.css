body {
    background-color: #fff;
  }
  #auth {
    height: 100vh;
    overflow-x: hidden;
  }
  #auth #auth-right {
    background: url(../../../mazer/images/4853433.png?45649b87e0b3f50bfa1372c6cdb4595f),
      linear-gradient(90deg, #7262a8, #aca4d4);
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  #auth #auth-left {
    padding: 5rem 8rem;
  }
  #auth #auth-left .auth-title {
    font-size: 4rem;
    margin-bottom: 1rem;
  }
  #auth #auth-left .auth-subtitle {
    color: #a8aebb;
    font-size: 1.7rem;
    line-height: 2.5rem;
  }
  #auth #auth-left .auth-logo {
    margin-bottom: 5rem;
  }
  /* #auth #auth-left .auth-logo img {
    height: 2rem;
  } */
  @media screen and (max-width: 767px) {
    #auth #auth-left {
      padding: 5rem;
    }
  }
  @media screen and (max-width: 576px) {
    #auth #auth-left {
      padding: 5rem 3rem;
    }
  }
  body.theme-dark #auth-right {
    background: url(../../../mazer/images/4853433.png?45649b87e0b3f50bfa1372c6cdb4595f),
      linear-gradient(90deg, #2d499d, #3f5491);
  }
  