<?php

namespace App\Http\Controllers\API\V3;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Order;
use App\Models\Company;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;

class ReportController extends Controller
{
    public function index(Request $request)
    {
        $user = getUser();
        
        try {
            // Special check for uplats246 employees
            if ($user['company']['id'] === 'c67c77a5-4caf-42eb-bad0-f1d5b181379e' && $user['userRole'] === 'staff') {
                return response()->json([
                    'status'      => '1',
                    'total'       => '0.00',
                    'daily_sales' => [],
                ]);
            }
            
            $startDate = Carbon::now()->startOfMonth();
            $endDate   = Carbon::now()->endOfMonth();
            
            if ($request->has('start_date') && $request->has('end_date')) {
                $startDate = Carbon::parse($request->start_date);
                $endDate   = Carbon::parse($request->end_date);
            } else if ($request->has('year_month')) {
                // Handle both formats: 2025_06 and 2025-06
                $yearMonth = str_replace('_', '-', $request->year_month);
                $startDate = Carbon::parse($yearMonth)->startOfMonth()->startOfDay();
                $endDate   = Carbon::parse($yearMonth)->endOfMonth()->endOfDay();

                // If the date range is within the current month, limit the end date to today.
                if ($yearMonth === Carbon::today()->format('Y-m')) {
                    $endDate = Carbon::today()->endOfDay();
                }
            }
            
            // Generate array of all dates in range
            $dateRange = collect();
            $currentDate = $startDate->copy();

            while ($currentDate->lte($endDate)) {
                $dateRange->push($currentDate->format('d/m/Y'));
                $currentDate->addDay();
            }
            // Reverse the date range if you want descending order
            $dateRange = $dateRange->reverse()->values();
            // Get actual sales data
            // If user is HQ, include sales from staff as well
            $userIds = [$user['id']]; // Start with current user

            if ($user['userRole'] === 'HQ') {
                // Get all staff user IDs under this HQ's company
                $staffUserIds = Employee::where('company_id', $user['company']['id'])
                    ->pluck('user_id')
                    ->toArray();

                // Merge HQ user ID with staff user IDs
                $userIds = array_merge($userIds, $staffUserIds);
                $userIds = array_unique($userIds); // Remove duplicates if any
            }

            $sales = Order::whereIn('user_id', $userIds)
                ->whereBetween('order_date', [$startDate, $endDate])
                ->selectRaw('DATE_FORMAT(order_date, "%d/%m/%Y") as date, SUM(grandtotal_decimal) as daily_sales')
                ->groupBy('date')
                ->orderBy('date', 'DESC')
                ->pluck('daily_sales', 'date');
            
            // Calculate total sales for the period
            $totalSales = $sales->sum();
            if (!$totalSales) {
                $totalSales = '0.00';
            } else {
                $totalSales = number_format($totalSales, 2, '.', ',');
            }
            
            // Map the date range to include zeros for missing dates
            $dailySales = $dateRange->map(function ($date) use ($sales) {
                return [
                    'date'        => $date,
                    'daily_sales' => number_format($sales[$date] ?? 0, 2, '.', ',')
                ];
            })->values();
                     
            return response()->json([
                'status'      => '1',
                'total'       => $totalSales,
                'daily_sales' => $dailySales,
            ]);
        } catch (\Exception $e) {
            Log::error('Error in ReportController index: ' . $e->getMessage());
            return response()->json(['status' => '0', 'message' => $e->getMessage()]);
        }
    }

    public function details(Request $request)
    {
        $date = $request->date;
        if(!$date){
            return response()->json(['status'=>'0','message'=>'Invalid date']);
        }
        $user = getUser();

        try {
            $today = Carbon::createFromFormat('d/m/Y', $date)->format('Y-m-d');
            if ($user['id']) {
                // Get user IDs to include in the report
                $userIds = [$user['id']]; // Start with current user

                if ($user['userRole'] === 'HQ') {
                    // Get all staff user IDs under this HQ's company
                    $staffUserIds = Employee::where('company_id', $user['company']['id'])
                        ->pluck('user_id')
                        ->toArray();

                    // Merge HQ user ID with staff user IDs
                    $userIds = array_merge($userIds, $staffUserIds);
                    $userIds = array_unique($userIds); // Remove duplicates if any
                }

                $report = $this->getSalesReportByPaymentMethodForMultipleUsers($userIds, $today);

                return response()->json($report);
            } else {
                return response()->json([
                    'status' => '0',
                    'message' => 'User pid not found'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error on report details : ' . $e->getMessage());
            return response()->json(['status' => '0', 'message' => $e->getMessage()]);
        }
    }

    /**
     * Get sales report by payment method for multiple users (HQ + staff)
     */
    private function getSalesReportByPaymentMethodForMultipleUsers($userIds, $date)
    {
        $payMethods = [
            'CASH',
            'DEBITCARD',
            'CREDITCARD',
            'BANKTRANSFER',
            'TOUCHNGO',
            'QRPAY',
            'Credit/Debit(NFC)',
            'ATOME',
            'GRABPAY',
            'BNPL',
            'EWALLET',
            'ScanQRGkash',
            'OTHERS'
        ];
        $counts = array_fill_keys($payMethods, 0);
        $totals = array_fill_keys($payMethods, 0.00);

        $orders = Order::select('pay_method', 'grandtotal_decimal')
            ->whereIn('user_id', $userIds)
            ->whereBetween('order_date', [$date . ' 00:00:00', $date . " 23:59:59"])
            ->whereNull('deleted_at')
            ->get();

        foreach ($orders as $order) {
            $payMethod = $order->pay_method;
            $grandtotal = $order->grandtotal_decimal;

            if (isset($counts[$payMethod])) {
                $counts[$payMethod]++;
                $totals[$payMethod] += $grandtotal;
            }
        }

        // Format the sums to 2 decimal places
        foreach ($totals as $payMethod => $total) {
            $totals[$payMethod] = number_format($total, 2, '.', '');
        }

        $total_count = number_format(array_sum($totals), 2, '.', ',');
        $trx = array_sum($counts);

        // Mapping array to rename output keys
        $renameMapping = [
            'BANKTRANSFER' => 'BANK TRANSFER',
            'DEBITCARD' => 'DEBIT CARD',
            'CREDITCARD' => 'CREDIT CARD',
            'TOUCHNGO' => 'TOUCH N GO',
            'Credit/Debit(NFC)' => 'NFC',
            'ScanQRGkash' => 'QR GKASH',
            'EWALLET' => 'E-WALLET'
        ];

        // Apply renaming to the final output
        $formattedCounts = [];
        $formattedTotals = [];

        foreach ($counts as $key => $value) {
            $newKey = $renameMapping[$key] ?? $key; // Use mapped name if available
            $formattedCounts[$newKey] = $value;
            $formattedTotals[$newKey] = number_format($totals[$key], 2, '.', ',');
        }

        return [
            'total' => $total_count,
            'trx' => $trx,
            'occurance' => $formattedCounts,
            'value' => $formattedTotals
        ];
    }
}
