<?php

namespace App\Exceptions;

use Throwable;
use App\Models\ExceptionLog;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;

class Handler extends ExceptionHandler
{
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    protected function unauthenticated($request, AuthenticationException $exception)
    {
        // Check if it's an API request (or request expects JSON response)
        if ($request->is('api/*') && $request->is('api/v3/order/*')) {
            // Return JSON response for API routes
            return response()->json([
                'success' => false,
                'status' => '0',
                'message' => 'Unauthenticated.',
                'error' => 'Authentication required to access this route.'
            ], 401);
        }

        // For web routes, retain the default redirect to login page
        return redirect()->guest(route('login'));
    }

    public function report(Throwable $exception)
    {
        // Only log exceptions that are not in the dontReport list
        if (!$this->shouldntReport($exception)) {
            $this->logException($exception);
        }

        parent::report($exception);
    }

    protected function logException(Throwable $exception)
    {
        try {
            // Check if exception_logs table exists before trying to log
            if (!Schema::hasTable('exception_logs')) {
                return;
            }

            // Determine severity based on exception type
            $severity = $this->determineSeverity($exception);

            ExceptionLog::create([
                'exception_class' => get_class($exception),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'severity' => $severity,
                'status' => 'new',
                'assigned_to' => $this->getAssignedUser()
            ]);
        } catch (\Exception $e) {
            // If logging to database fails, just continue without logging
            // This prevents recursive exceptions during migrations
            return;
        }
    }

    protected function determineSeverity(Throwable $exception): string
    {
        // Custom logic to determine severity
        if ($exception instanceof \Symfony\Component\HttpKernel\Exception\HttpException) {
            $statusCode = $exception->getStatusCode();
            if ($statusCode >= 500) return 'critical';
            if ($statusCode >= 400) return 'high';
        }

        return 'medium';
    }

    protected function getAssignedUser(): ?int
    {
        $userId = session('user_id'); 
        
        // Cast to int if coming as string
        return is_numeric($userId) ? (int)$userId : null;
        
        // OR for database results
        return auth()->user()?->id; // returns int|null
    }
}
