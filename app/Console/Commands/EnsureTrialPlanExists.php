<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\TrialPlanSeeder;

class EnsureTrialPlanExists extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscription:ensure-trial-plan';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Ensures that the 7-day trial plan exists in the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking for trial plan...');
        
        $seeder = new TrialPlanSeeder();
        $seeder->run();
        
        $this->info('Trial plan check completed.');
        
        return Command::SUCCESS;
    }
}
