<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Feature;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if features table exists before proceeding
        if (!Schema::hasTable('features')) {
            return;
        }

        // Check if features already exist to avoid duplicates
        $existingFeature = Feature::where('code', 'unlimited_product_sku')->first();
        if ($existingFeature) {
            return;
        }

        // Create limit upgrade features
        $limitUpgradeFeatures = [
            [
                'name' => 'Unlimited Product SKUs',
                'code' => 'unlimited_product_sku',
                'description' => 'Remove the limit on the number of product SKUs you can create',
                'type' => 'limit_upgrade',
                'price' => 19.99,
                'is_active' => true,
                'metadata' => json_encode([
                    'description' => 'Upgrade to unlimited product SKUs',
                    'base_feature' => 'product_sku_limit',
                    'upgrade_type' => 'unlimited',
                    'icon' => 'fas fa-box'
                ])
            ],
            [
                'name' => '500 Additional Product SKUs',
                'code' => 'additional_500_product_sku',
                'description' => 'Add 500 more product SKUs to your current limit',
                'type' => 'limit_upgrade',
                'price' => 9.99,
                'is_active' => true,
                'metadata' => json_encode([
                    'description' => 'Add 500 more product SKUs',
                    'base_feature' => 'product_sku_limit',
                    'upgrade_type' => 'additional',
                    'upgrade_value' => 500,
                    'icon' => 'fas fa-plus'
                ])
            ],
            [
                'name' => 'Unlimited Staff Accounts',
                'code' => 'unlimited_staff_account',
                'description' => 'Remove the limit on the number of staff accounts you can create',
                'type' => 'limit_upgrade',
                'price' => 14.99,
                'is_active' => true,
                'metadata' => json_encode([
                    'description' => 'Upgrade to unlimited staff accounts',
                    'base_feature' => 'staff_account_limit',
                    'upgrade_type' => 'unlimited',
                    'icon' => 'fas fa-users'
                ])
            ],
            [
                'name' => '5 Additional Staff Accounts',
                'code' => 'additional_5_staff_account',
                'description' => 'Add 5 more staff accounts to your current limit',
                'type' => 'limit_upgrade',
                'price' => 7.99,
                'is_active' => true,
                'metadata' => json_encode([
                    'description' => 'Add 5 more staff accounts',
                    'base_feature' => 'staff_account_limit',
                    'upgrade_type' => 'additional',
                    'upgrade_value' => 5,
                    'icon' => 'fas fa-user-plus'
                ])
            ]
        ];

        foreach ($limitUpgradeFeatures as $featureData) {
            Feature::create($featureData);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Check if features table exists before proceeding
        if (!Schema::hasTable('features')) {
            return;
        }

        // Remove the limit upgrade features
        $featureCodes = [
            'unlimited_product_sku',
            'additional_500_product_sku',
            'unlimited_staff_account',
            'additional_5_staff_account'
        ];

        Feature::whereIn('code', $featureCodes)->delete();
    }
};
