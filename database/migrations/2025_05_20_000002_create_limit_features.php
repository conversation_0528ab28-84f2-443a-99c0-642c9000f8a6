<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Feature;
use App\Models\Subscription\SubscriptionPlan;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if features table exists before proceeding
        if (!Schema::hasTable('features')) {
            return;
        }

        // Check if features already exist to avoid duplicates
        $existingFeature = Feature::where('code', 'product_sku_limit')->first();
        if ($existingFeature) {
            return;
        }

        // Create new features for limits
        $features = [
            [
                'name' => 'Product SKU Limit',
                'code' => 'product_sku_limit',
                'description' => 'Maximum number of product SKUs that can be created',
                'type' => 'core',
                'price' => 0,
                'is_active' => true,
                'metadata' => json_encode(['description' => 'Controls the maximum number of product SKUs'])
            ],
            [
                'name' => 'Staff Account Limit',
                'code' => 'staff_account_limit',
                'description' => 'Maximum number of staff accounts that can be created',
                'type' => 'core',
                'price' => 0,
                'is_active' => true,
                'metadata' => json_encode(['description' => 'Controls the maximum number of staff accounts'])
            ]
        ];

        foreach ($features as $feature) {
            Feature::create($feature);
        }

        // Get the features we just created
        $productSkuLimitFeature = Feature::where('code', 'product_sku_limit')->first();
        $staffAccountLimitFeature = Feature::where('code', 'staff_account_limit')->first();

        // Check if subscription_plans table exists before proceeding
        if (!Schema::hasTable('subscription_plans')) {
            return;
        }

        // Get all subscription plans
        $plans = SubscriptionPlan::all();

        // Set default limits based on tier
        foreach ($plans as $plan) {
            $tier = $plan->tier;
            
            // Default limits based on tier
            $skuLimit = 100; // Default for starter
            $staffLimit = 2;  // Default for starter
            
            if ($tier === 'plus') {
                $skuLimit = 500;
                $staffLimit = 5;
            } elseif ($tier === 'advance') {
                $skuLimit = 1000;
                $staffLimit = 10;
            }
            
            // Attach features with limits to the plan
            if ($productSkuLimitFeature) {
                $plan->features()->attach($productSkuLimitFeature->id, [
                    'settings' => json_encode(['limit' => $skuLimit])
                ]);
            }
            
            if ($staffAccountLimitFeature) {
                $plan->features()->attach($staffAccountLimitFeature->id, [
                    'settings' => json_encode(['limit' => $staffLimit])
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Check if features table exists before proceeding
        if (!Schema::hasTable('features')) {
            return;
        }

        // Get the features
        $productSkuLimitFeature = Feature::where('code', 'product_sku_limit')->first();
        $staffAccountLimitFeature = Feature::where('code', 'staff_account_limit')->first();

        // Check if subscription_plans table exists before proceeding
        if (!Schema::hasTable('subscription_plans')) {
            // Just delete the features if subscription_plans table doesn't exist
            if ($productSkuLimitFeature) {
                $productSkuLimitFeature->delete();
            }

            if ($staffAccountLimitFeature) {
                $staffAccountLimitFeature->delete();
            }
            return;
        }

        // Get all subscription plans
        $plans = SubscriptionPlan::all();

        // Detach features from plans
        foreach ($plans as $plan) {
            if ($productSkuLimitFeature) {
                $plan->features()->detach($productSkuLimitFeature->id);
            }
            
            if ($staffAccountLimitFeature) {
                $plan->features()->detach($staffAccountLimitFeature->id);
            }
        }

        // Delete the features
        if ($productSkuLimitFeature) {
            $productSkuLimitFeature->delete();
        }
        
        if ($staffAccountLimitFeature) {
            $staffAccountLimitFeature->delete();
        }
    }
};