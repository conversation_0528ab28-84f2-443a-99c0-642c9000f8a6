<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTitleToReceiptsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check if receipts table exists before trying to modify it
        if (!Schema::hasTable('receipts')) {
            return;
        }

        // Check if title column already exists
        if (Schema::hasColumn('receipts', 'title')) {
            return;
        }

        Schema::table('receipts', function (Blueprint $table) {
            $table->string('title')->after('user_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Check if receipts table exists before trying to modify it
        if (!Schema::hasTable('receipts')) {
            return;
        }

        // Check if title column exists before trying to drop it
        if (!Schema::hasColumn('receipts', 'title')) {
            return;
        }

        Schema::table('receipts', function (Blueprint $table) {
            $table->dropColumn('title');
        });
    }
} 