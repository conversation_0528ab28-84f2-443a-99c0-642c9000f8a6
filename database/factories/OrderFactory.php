<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Order;
use App\Models\Company;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    protected $model = Order::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $grandtotal = fake()->randomFloat(2, 10, 1000);
        
        return [
            'user_id' => User::factory(),
            'bizapp_order_id' => fake()->unique()->numerify('ORD########'),
            'bizapp_temp_id' => fake()->unique()->numerify('TMP########'),
            'compaign_id' => null,
            'company_id' => Company::factory(),
            'customer_id' => null,
            'customer_name' => fake()->name(),
            'order_number' => fake()->unique()->numerify('ON########'),
            'pay_method' => fake()->randomElement(['CASH', 'DEBITCARD', 'CREDITCARD', 'BANKTRANSFER', 'TOUCHNGO', 'QRPAY']),
            'cap_id' => null,
            'cartID' => null,
            'pay_collection_id' => null,
            'roundingAdjustment' => '0.00',
            'subtotal' => number_format($grandtotal, 2, '.', ''),
            'payment_received' => number_format($grandtotal, 2, '.', ''),
            'payment_balance' => '0.00',
            'grandtotal' => number_format($grandtotal, 2, '.', ''),
            'discounts' => '0.00',
            'roundingAdjustment_decimal' => 0.00,
            'subtotal_decimal' => $grandtotal,
            'payment_received_decimal' => $grandtotal,
            'payment_balance_decimal' => 0.00,
            'grandtotal_decimal' => $grandtotal,
            'discounts_decimal' => 0.00,
            'order_label' => null,
            'order_image' => null,
            'order_notes' => null,
            'order_latitude' => null,
            'order_longitude' => null,
            'order_date' => Carbon::today(),
            'status' => 'completed',
            'payment_provider' => null,
            'coupon_code' => null,
            'coupon_value' => null,
            'coupon_for' => null,
            'sst_value' => 0.00,
            'sst_amount' => 0.00,
            'order_items' => null,
        ];
    }
}
