<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\UserDetail;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserDetail>
 */
class UserDetailFactory extends Factory
{
    protected $model = UserDetail::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'first_name' => fake()->firstName(),
            'last_name' => fake()->lastName(),
            'mobile' => fake()->phoneNumber(),
            'avatar' => null,
            'address' => fake()->address(),
            'city' => fake()->city(),
            'state' => fake()->state(),
            'postcode' => fake()->postcode(),
            'country' => 'Malaysia',
            'currency' => 'MYR',
            'bizapp_secretkey' => null,
            'status' => 'active',
            'device_info' => null,
            'app_version' => null,
            'bizapp_exp_date' => null,
            'device_brand' => null,
            'device_id' => null,
            'db_version' => null,
            'device_os' => null,
        ];
    }
}
