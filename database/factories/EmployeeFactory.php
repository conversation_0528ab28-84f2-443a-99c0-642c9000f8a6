<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Company;
use App\Models\Employee;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Employee>
 */
class EmployeeFactory extends Factory
{
    protected $model = Employee::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'boss_id' => User::factory(),
            'company_id' => Company::factory(),
            'emp_jobTitle' => 'staff',
            'emp_status' => 'active',
            'total_sales' => 0,
            'join_date' => fake()->date(),
        ];
    }
}
