<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Company;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Company>
 */
class CompanyFactory extends Factory
{
    protected $model = Company::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'com_name' => fake()->company(),
            'com_address' => fake()->address(),
            'com_city' => fake()->city(),
            'com_state' => fake()->state(),
            'com_postcode' => fake()->postcode(),
            'com_country' => 'Malaysia',
            'com_registration_no' => fake()->numerify('##########'),
            'com_mobile' => fake()->phoneNumber(),
            'com_email' => fake()->companyEmail(),
            'com_sst_value' => 6.00,
            'com_sst_number' => null,
            'status' => 'active',
            'category_id' => null,
            'pbt_id' => null,
        ];
    }
}
