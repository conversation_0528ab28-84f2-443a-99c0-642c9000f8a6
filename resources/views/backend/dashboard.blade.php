@extends('backend.layout.default')

@section('content')

<div class="page-content">
    @if(isset($pendingSubscription))
    <div class="alert alert-warning alert-dismissible fade show" role="alert">
        <h4 class="alert-heading">Subscription Payment Required</h4>
        <p>You have a pending payment for your <strong>{{ $pendingSubscription->subscriptionPlan->name }}</strong> subscription plan.</p>
        <p>Price: RM{{ number_format($pendingSubscription->subscriptionPlan->price, 2) }}</p>
        <hr>
        <p class="mb-0">
            <a href="{{ route('payment.subscription', ['subscription' => $pendingSubscription->id]) }}" class="btn btn-primary">
                Proceed to Payment
            </a>
        </p>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif
    <section class="row">
      <div class="col-12 col-lg-9">
        <div class="row">

          <div class="col-6 col-lg-6 col-md-6">
            <div class="card">
              <div class="card-body px-4 py-4-5">
                <div class="row">
                  <div
                    class="col-md-4 col-lg-12 col-xl-12 col-xxl-5 d-flex justify-content-start"
                  >
                    <div class="stats-icon purple mb-2">
                      <i class="iconly-boldShow"></i>
                    </div>
                  </div>
                  <div class="col-md-8 col-lg-12 col-xl-12 col-xxl-7">
                    <h6 class="text-muted font-semibold">
                      {{ date('Y') }} Sales Figure ({{ auth()->user()->userDetails->currency ?? 'RM' }})
                    </h6>
                    <h6 class="font-extrabold mb-0">{{ $getTotalSales == null ? "0.00" : number_format($getTotalSales,2) }}</h6>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-6 col-lg-6 col-md-6">
            <div class="card">
              <div class="card-body px-4 py-4-5">
                <div class="row">
                  <div
                    class="col-md-4 col-lg-12 col-xl-12 col-xxl-5 d-flex justify-content-start"
                  >
                    <div class="stats-icon red mb-2">
                      <i class="iconly-boldBookmark"></i>
                    </div>
                  </div>
                  <div class="col-md-8 col-lg-12 col-xl-12 col-xxl-7">
                    <h6 class="text-muted font-semibold">{{ date('Y') }} Transactions</h6>
                    <h6 class="font-extrabold mb-0">{{ $totalTrx }}</h6>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h4>Daily Sales Data</h4>
              </div>
              <div class="card-body">
                <div id="chart-daily-sales"></div>
              </div>
            </div>
          </div>
        </div>
        {{-- @if (auth()->user()->username === 'wowskin') --}}
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h4>Order Activity Analysis</h4>
              <p>When customers place orders ({{ date('Y') }})</p>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-12">
                  <h5 class="text-center">Orders by Hour of Day</h5>
                  <div id="chart-hourly-orders"></div>
                </div>
                <div class="col-md-12">
                  <h5 class="text-center">Orders by Day of Week</h5>
                  <div id="chart-daily-orders"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {{-- @endif --}}

        <div class="row">
          <div class="col-12 col-xl-4">
            <div class="card">
              <div class="card-header">
                <h4>Top Payment Method</h4>
                <p>Data for {{ date('Y') }}</p>
              </div>
              <div class="card-body">
                <div id="chart-top-payment-method"></div>
              </div>
            </div>
          </div>
          <div class="col-12 col-xl-4">
            <div class="card">
              <div class="card-header">
                <h4>Top Products</h4>
                <p>Data for {{ date('Y') }}</p>
              </div>
              <div class="card-body">
                <div id="chart-top-products"></div>
              </div>
            </div>
          </div>

        </div>
      </div>
      <div class="col-12 col-lg-3">

        <div class="card">
          <div class="card-header">
            <h4>Recent Sale</h4>
          </div>
          <div class="card-content pb-4">
            @foreach ($last3Orders as $item)

              <div class="recent-message d-flex px-4 py-3">
                <div class="avatar avatar-lg">
                  @if ($item->order_image != null)
                  <img src="{{ url('https://corrad.visionice.net/bizapp/upload/product/' . $item->order_image) }}" />
                  @else
                  <img src="mazer/images/image-placeholder.jpeg" />
                  @endif
                </div>
                <div class="name ms-4" style="
                            max-width: 100%;
                            flex: 1;
                            overflow: hidden;
                        ">
                  <h5 class="mb-1">{{ $item->orderDetails[0]['product_SKU'] }}</h5>
                  <h6 class="text-muted mb-0">{{ \Carbon\Carbon::parse($item->order_date)->diffForHumans() }}</h6>
                </div>
              </div>
            @endforeach
          </div>
        </div>

      </div>
    </section>
  </div>

  @stop

@push('scripts')

  <script>
    lastWeek = @json($lastWeek);
    let top3products = @json($top3products);
    let topPaymentMethod = @json($topPaymentMethod);
    let hourlyOrderData = @json($hourlyOrderData);
    let dailyOrderData = @json($dailyOrderData);
 </script>

 <script src="{{ asset('mazer/extensions/apexcharts/apexcharts.js') }}"></script>
 <script src="{{ asset('mazer/js/pages/dashboard.js') }}?v=1.2"></script>
 @endpush
