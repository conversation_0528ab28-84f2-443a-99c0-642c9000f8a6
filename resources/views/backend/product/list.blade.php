<div class="table-responsive">
    <table class="table table-sm table-hover table-nowrap card-table" id="table">
        <thead>
            <tr>
                <th style="width: 5%;">Image</th>
                <th style="width: {{ auth()->user()->companies ? '40%' : '60%' }};">Name</th>
                <th style="width: 10%;">SKU</th>
                <th style="width: 10%;">Stock</th>
                <th style="width: 15%;">Price</th>
                @if (auth()->user()->companies)
                <th style="width: 20%;">Action</th>
                @endif  
            </tr>
        </thead>
        <tbody class="list fs-base">
            @forelse ($list as $item)
            <tr>
                <td>
                     <img 
                                src="{{ $item->product_attachment ? 'https://corrad.visionice.net/bizapp/upload/product/'.$item->product_attachment : asset('mazer/images/image-placeholder.jpeg') }}"
                                alt="{{{ $item->product_name }}}" 
                                class="img-thumbnail" 
                                style="width: 50px; height: 50px; object-fit: cover;"
                            >
                </td>
                <td>{{{ $item->product_name }}}</td>
                <td>{{{ $item->product_SKU }}}</td>
                <td>{{{ ($item->product_stock_status == "Y") ? "Ready Stock" : $item->product_stock }}}</td>
                <td>{{{ number_format($item->product_price, 2, '.', '') }}}</td>
                <td>
                    <div class="form-group">
                        @if (auth()->user()->companies)
                        <a href="{{ route('product.edit', $item->id) }}" class="btn btn-sm btn-primary">Edit</a>   
                        <button class="btn btn-sm btn-danger" id="deleteProd" data-id="{{ $item->id }}">Delete</button>
                        @endif
                    </div>
                </td>
            @empty
            <tr>
                <td colspan="6" class="text-center">No data</td>
            </tr>
            @endforelse
        </tbody>
    </table>
</div>
<div class="card-footer d-flex justify-content-between">
    {!! $list->onEachSide(5)->links('backend.layout.pagination') !!}
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteProductModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this product?
            </div>
            <div class="modal-footer">
                <form id="deleteForm" method="POST" action="">
                    @csrf
                    @method('DELETE')
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>

    $(document).ready(function(){
    $('#table').on('click', '#deleteProd', function(){
        const id = $(this).data('id');
        const url = '{{ route('product.delete', ':id') }}'.replace(':id', id);
        Swal.fire({
            title: 'Confirm Delete',
            text: "Are you sure you want to delete this product?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: url,
                    type: 'DELETE',
                    data: {
                        "_token": "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        Swal.fire(
                            'Deleted!',
                            'The product has been deleted.',
                            'success'
                        ).then(() => {
                            location.reload();
                        });
                    },
                    error: function(xhr) {
                        try {
                            const responseJson = JSON.parse(xhr.responseText);
                            console.log('Parsed response:', responseJson);
                        } catch (e) {
                            console.log('Could not parse response as JSON');
                        }
                        Swal.fire(
                            'Error!',
                            'There was an error deleting the product.',
                            'error'
                        );
                        // console.error(xhr.responseText);
                    }
                });
            }
        });
    });
});
</script>

