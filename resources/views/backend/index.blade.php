<!DOCTYPE html>
<html lang="en">
  <head>
    @section('title', 'Dashboard')

    @include('./backend/partials/head')

    <link rel="stylesheet" href="mazer/css/shared/iconly.css" />
  </head>

  <body>
    <script src="mazer/js/initTheme.js"></script>
    <div id="app">
      @include('./backend/partials/sidebar')
      <div id="main">
        <header class="mb-3 d-flex justify-content-between align-items-center">
          <a href="#" class="burger-btn d-block d-xl-none">
            <i class="bi bi-justify fs-3"></i>
          </a>
          <a href="{{ route('profile') }}" class="profile-btn d-block d-xl-none">
            {{-- <img src="/path/to/profile-image.jpg" alt="Profile" class="rounded-circle" width="40" height="40"> --}}
            {{-- @if (auth()->user()->userDetails->avatar != null && auth()->user()->isBizappUser == 'N') --}}
            {{-- <img src="{{ url('uploads/images/useravatar/' . auth()->user()->userDetails->avatar) }}" title="navbar-avatar" alt="Profile" class="rounded-circle" width="40" height="40"> --}}
          {{-- @elseif (auth()->user()->userDetails->avatar != null && auth()->user()->isBizappUser == 'Y') --}}
            {{-- <img src="{{ ('https://corrad.visionice.net/bizapp/upload/profile/' . auth()->user()->userDetails->avatar) }}" title="navbar-avatar" alt="Profile" class="rounded-circle" width="40" height="40"> --}}
            {{-- @else --}}
            <img src="{{ asset('mazer/images/faces/1.jpg')}}" title="navbar-avatar" class="rounded-circle" width="40" height="40">
          {{-- @endif --}}
        </a>
        </header>

        <div class="d-lg-block d-none page-heading">
            @include('./backend/partials/navbar') 
          {{-- <h3>My Dashboard</h3> --}}
        </div>

        

        <footer>
          @include('./backend/partials/footer')
        </footer>
      </div>
    </div>
   

    @include('./backend/partials/footer-menu')
       
  
  </body>
</html>
