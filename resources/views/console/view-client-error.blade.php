<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Error Details</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.0/font/bootstrap-icons.css">
    <style>
        .error-container {
            margin-top: 30px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <div class="container error-container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Client Error #{{ $error->id }}</h2>
            <a href="{{ route('console.logs', ['tab' => 'client_errors']) }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Logs
            </a>
        </div>

        <div class="card">
            <div class="card-body">
                <table class="table table-bordered">
                    <tbody>
                         <tr>
                            <th width="20%">Error Code</th>
                            <td>
                                <pre class="error-code" data-json="{{ $error->error_code }}">{{ $error->error_code }}</pre>
                            </td>
                        </tr>
                        <tr>
                            <th width="20%">Error Message</th>
                            <td>
                                <pre data-json="{{ $error->error_message }}">{{ $error->error_message }}</pre>
                            </td>
                        </tr>
                        <tr>
                            <th>Occurred At</th>
                            <td>{{ $error->created_at->format('Y-m-d H:i:s') }}</td>
                        </tr>
                         @if($error->user_id)
                        <tr>
                            <th>User ID</th>
                            <td>
                                {{ $error->user_id }}
                                @if($error->user)
                                    ({{ $error->user->username }})
                                @endif
                            </td>
                        </tr>
                        @endif
                         @if($error->device_id)
                        <tr>
                            <th>Device ID</th>
                            <td>{{ $error->device_id }}</td>
                        </tr>
                        @endif
                        @if($error->stack_trace)
                        <tr>
                            <th>Stack Trace</th>
                            <td>
                                <pre data-json="{{ $error->stack_trace }}">{{ $error->stack_trace }}</pre>
                            </td>
                        </tr>
                        @endif
                       
                        @if($error->data)
                        <tr>
                            <th>JSON data</th>
                            <td>
                                <pre data-json="{{ $error->data }}">{{ $error->data }}</pre>
                                <button class="btn btn-sm btn-outline-primary copy-json" data-content="{{ $error->data }}">
                                    <i class="bi bi-clipboard"></i> Copy
                                </button>
                            </td>
                        </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function formatJson(jsonString) {
            try {
                const jsonObj = JSON.parse(jsonString);
                return JSON.stringify(jsonObj, null, 4);
            } catch (e) {
                return jsonString;
            }
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                const toast = document.createElement('div');
                toast.className = 'alert alert-success position-fixed bottom-0 end-0 m-3';
                toast.style.zIndex = '9999';
                toast.textContent = 'Copied to clipboard!';
                document.body.appendChild(toast);
                setTimeout(() => toast.remove(), 2000);
            });
        }

        document.addEventListener('DOMContentLoaded', () => {
            document.querySelectorAll('pre[data-json]').forEach(pre => {
                const formatted = formatJson(pre.textContent);
                if (formatted !== pre.textContent) {
                    pre.innerHTML = syntaxHighlight(formatted);
                }
            });

            document.querySelectorAll('.copy-json').forEach(button => {
                button.addEventListener('click', () => {
                    copyToClipboard(button.dataset.content);
                });
            });
        });

        function syntaxHighlight(json) {
            json = json.replace(/&/g, '&').replace(/</g, '<').replace(/>/g, '>');
            return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, 
                match => {
                    let cls = 'text-dark';
                    if (/^"/.test(match)) {
                        if (/:$/.test(match)) {
                            cls = 'text-primary';
                        } else {
                            cls = 'text-success';
                        }
                    } else if (/true|false/.test(match)) {
                        cls = 'text-info';
                    } else if (/null/.test(match)) {
                        cls = 'text-danger';
                    }
                    return `<span class="${cls}">${match}</span>`;
                });
        }
    </script>

    <style>
        .copy-json {
            margin-top: 5px;
            margin-left: 5px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 500px;
            overflow-y: auto;
        }
        /* Add horizontal scroll for JSON data */
        pre[data-json] {
            max-width: 100%;
            overflow-x: auto;
        }
        .error-code {
            color: red;
        }
        .text-primary { color: #0d6efd; }
        .text-success { color: #198754; }
        .text-info { color: #0dcaf0; }
        .text-danger { color: #dc3545; }
        .text-dark { color: #212529; }
    </style>
</body>
</html>
